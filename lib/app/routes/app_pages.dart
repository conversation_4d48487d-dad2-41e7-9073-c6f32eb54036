import 'package:get/get.dart';

import '../modules/add_task/add_task_binding.dart';
import '../modules/add_task/add_task_view.dart';
import '../modules/home/<USER>';
import '../modules/home/<USER>';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.HOME;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.ADD_TASK,
      page: () => const AddTaskView(),
      binding: AddTaskBinding(),
    ),
  ];
}
